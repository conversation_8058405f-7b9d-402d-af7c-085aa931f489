#!/usr/bin/env node

/**
 * Diagnostic script for Mistral auto-start issues
 */

import { exec, spawn } from 'child_process';
import http from 'http';
import fs from 'fs';
import { promisify } from 'util';

const execAsync = promisify(exec);

console.log('🔍 Diagnosing Mistral Auto-Start Issues...\n');

// Test results
const results = {
    nodejs: false,
    autoStartServer: false,
    docker: false,
    port8000: false,
    port3001: false,
    dependencies: false
};

async function checkNodeJS() {
    console.log('1️⃣ Checking Node.js...');
    return new Promise((resolve) => {
        exec('node --version', (error, stdout, stderr) => {
            if (error) {
                console.log('   ❌ Node.js not found or not in PATH');
                console.log(`   Error: ${error.message}`);
                results.nodejs = false;
            } else {
                console.log(`   ✅ Node.js found: ${stdout.trim()}`);
                results.nodejs = true;
            }
            resolve();
        });
    });
}

async function checkDependencies() {
    console.log('\n2️⃣ Checking dependencies...');
    
    // Check if package files exist
    if (fs.existsSync('package-mistral.json')) {
        console.log('   ✅ package-mistral.json found');
    } else {
        console.log('   ❌ package-mistral.json not found');
        return;
    }
    
    if (fs.existsSync('mistral-auto-server.js')) {
        console.log('   ✅ mistral-auto-server.js found');
    } else {
        console.log('   ❌ mistral-auto-server.js not found');
        return;
    }
    
    // Check if node_modules exists
    if (fs.existsSync('node_modules')) {
        console.log('   ✅ node_modules directory found');
        results.dependencies = true;
    } else {
        console.log('   ⚠️ node_modules not found - dependencies need to be installed');
        console.log('   Run: npm install express cors');
        results.dependencies = false;
    }
}

async function checkPorts() {
    console.log('\n3️⃣ Checking ports...');
    
    // Check port 3001 (auto-start server)
    await checkPort(3001, 'Auto-start server');
    
    // Check port 8000 (Mistral server)
    await checkPort(8000, 'Mistral server');
}

async function checkPort(port, description) {
    return new Promise((resolve) => {
        const req = http.request({
            hostname: 'localhost',
            port: port,
            path: '/',
            method: 'GET',
            timeout: 2000
        }, (res) => {
            console.log(`   ✅ Port ${port} (${description}) is responding`);
            if (port === 3001) results.port3001 = true;
            if (port === 8000) results.port8000 = true;
            resolve();
        });
        
        req.on('error', (error) => {
            console.log(`   ❌ Port ${port} (${description}) not responding: ${error.message}`);
            resolve();
        });
        
        req.on('timeout', () => {
            console.log(`   ❌ Port ${port} (${description}) timeout`);
            req.destroy();
            resolve();
        });
        
        req.end();
    });
}

async function checkDocker() {
    console.log('\n4️⃣ Checking Docker...');
    return new Promise((resolve) => {
        exec('docker --version', (error, stdout, stderr) => {
            if (error) {
                console.log('   ❌ Docker not found or not running');
                console.log(`   Error: ${error.message}`);
                results.docker = false;
            } else {
                console.log(`   ✅ Docker found: ${stdout.trim()}`);
                results.docker = true;
                
                // Check if Docker is actually running
                exec('docker ps', (psError, psStdout, psStderr) => {
                    if (psError) {
                        console.log('   ⚠️ Docker found but not running or not accessible');
                        console.log(`   Error: ${psError.message}`);
                        results.docker = false;
                    } else {
                        console.log('   ✅ Docker is running and accessible');
                        results.docker = true;
                    }
                    resolve();
                });
            }
        });
    });
}

async function checkAutoStartServer() {
    console.log('\n5️⃣ Checking auto-start server status...');
    
    return new Promise((resolve) => {
        const req = http.request({
            hostname: 'localhost',
            port: 3001,
            path: '/health',
            method: 'GET',
            timeout: 5000
        }, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const health = JSON.parse(data);
                    console.log(`   ✅ Auto-start server is running: ${health.service}`);
                    results.autoStartServer = true;
                } catch (error) {
                    console.log('   ⚠️ Auto-start server responding but invalid JSON');
                }
                resolve();
            });
        });
        
        req.on('error', (error) => {
            console.log(`   ❌ Auto-start server not accessible: ${error.message}`);
            results.autoStartServer = false;
            resolve();
        });
        
        req.on('timeout', () => {
            console.log('   ❌ Auto-start server timeout');
            req.destroy();
            results.autoStartServer = false;
            resolve();
        });
        
        req.end();
    });
}

async function provideSolutions() {
    console.log('\n🔧 Solutions and Next Steps:\n');
    
    if (!results.nodejs) {
        console.log('❌ CRITICAL: Install Node.js');
        console.log('   Download from: https://nodejs.org/');
        console.log('   Make sure it\'s added to your PATH\n');
        return;
    }
    
    if (!results.dependencies) {
        console.log('📦 INSTALL DEPENDENCIES:');
        console.log('   npm install express cors');
        console.log('   Or run: npm install --package-lock-only=false express cors\n');
    }
    
    if (!results.autoStartServer) {
        console.log('🚀 START AUTO-START SERVER:');
        console.log('   Windows: start-mistral-auto.bat');
        console.log('   Linux/Mac: ./start-mistral-auto.sh');
        console.log('   Manual: node mistral-auto-server.js\n');
    }
    
    if (!results.docker) {
        console.log('🐳 DOCKER OPTIONS:');
        console.log('   Option 1: Install Docker Desktop');
        console.log('   Option 2: Use mock server (automatic fallback)');
        console.log('   Option 3: Manual Mistral installation\n');
    }
    
    if (!results.port8000 && results.autoStartServer) {
        console.log('🎯 TRY STARTING MISTRAL:');
        console.log('   The auto-start server is running, try these:');
        console.log('   1. Open PDF converter and switch to Mistral engine');
        console.log('   2. Click "Test Connection" button');
        console.log('   3. Check the conversion log for detailed errors\n');
    }
    
    console.log('🧪 QUICK TEST:');
    console.log('   Open: http://localhost:5173/test-auto-start.html');
    console.log('   This will test all components automatically\n');
    
    console.log('📋 MANUAL FALLBACK:');
    console.log('   If all else fails, you can:');
    console.log('   1. Use Azure Document Intelligence instead');
    console.log('   2. Set up Mistral manually (see MISTRAL_SETUP.md)');
    console.log('   3. Use the mock server for testing\n');
}

async function runDiagnostics() {
    await checkNodeJS();
    await checkDependencies();
    await checkPorts();
    await checkDocker();
    await checkAutoStartServer();
    
    console.log('\n📊 DIAGNOSTIC SUMMARY:');
    console.log('========================');
    Object.entries(results).forEach(([key, value]) => {
        const icon = value ? '✅' : '❌';
        const name = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        console.log(`${icon} ${name}: ${value ? 'OK' : 'FAILED'}`);
    });
    
    await provideSolutions();
}

// Run diagnostics
runDiagnostics().catch(console.error);
