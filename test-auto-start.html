<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auto-Start Feature</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Auto-Start Feature Test</h1>
        <p>This page tests the Mistral auto-start functionality.</p>
        
        <div class="test-container">
            <h3>📋 Test Checklist</h3>
            <div id="test-results">
                <div class="status-indicator status-info">
                    <span>⏳</span>
                    <span>Starting tests...</span>
                </div>
            </div>
        </div>
        
        <div class="test-container">
            <h3>🎮 Manual Tests</h3>
            <button onclick="testAutoStartServer()">Test Auto-Start Server</button>
            <button onclick="testDockerCheck()">Test Docker Check</button>
            <button onclick="testMockServer()">Test Mock Server</button>
            <button onclick="testFullWorkflow()">Test Full Workflow</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="test-container">
            <h3>📊 Test Log</h3>
            <div id="test-log" class="log-output">Ready to run tests...</div>
        </div>
    </div>

    <script>
        const testLog = document.getElementById('test-log');
        const testResults = document.getElementById('test-results');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            testLog.textContent += logEntry;
            testLog.scrollTop = testLog.scrollHeight;
            console.log(`[AUTO-START TEST] ${message}`);
        }
        
        function clearLog() {
            testLog.textContent = 'Log cleared.\n';
        }
        
        function updateTestResult(test, status, message) {
            const statusClass = {
                'success': 'status-success',
                'error': 'status-error',
                'warning': 'status-warning',
                'info': 'status-info'
            };
            
            const icons = {
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'info': '⏳'
            };
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `status-indicator ${statusClass[status]}`;
            resultDiv.innerHTML = `<span>${icons[status]}</span><span>${test}: ${message}</span>`;
            
            // Remove existing result for this test
            const existing = Array.from(testResults.children).find(el => 
                el.textContent.includes(test + ':')
            );
            if (existing) {
                existing.remove();
            }
            
            testResults.appendChild(resultDiv);
        }
        
        async function testAutoStartServer() {
            log('Testing auto-start server...', 'info');
            updateTestResult('Auto-Start Server', 'info', 'Testing...');
            
            try {
                const response = await fetch('http://localhost:3001/health');
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Auto-start server is running: ${JSON.stringify(data)}`, 'success');
                    updateTestResult('Auto-Start Server', 'success', 'Running on port 3001');
                } else {
                    log(`❌ Auto-start server returned ${response.status}`, 'error');
                    updateTestResult('Auto-Start Server', 'error', `HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ Auto-start server not accessible: ${error.message}`, 'error');
                updateTestResult('Auto-Start Server', 'error', 'Not running or not accessible');
            }
        }
        
        async function testDockerCheck() {
            log('Testing Docker check...', 'info');
            updateTestResult('Docker Check', 'info', 'Testing...');
            
            try {
                const response = await fetch('http://localhost:3001/api/docker/check', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
                const data = await response.json();
                if (data.available) {
                    log(`✅ Docker is available: ${data.version}`, 'success');
                    updateTestResult('Docker Check', 'success', `Available (${data.version})`);
                } else {
                    log(`⚠️ Docker not available: ${data.error}`, 'warning');
                    updateTestResult('Docker Check', 'warning', 'Not available');
                }
            } catch (error) {
                log(`❌ Docker check failed: ${error.message}`, 'error');
                updateTestResult('Docker Check', 'error', 'Check failed');
            }
        }
        
        async function testMockServer() {
            log('Testing mock server start...', 'info');
            updateTestResult('Mock Server', 'info', 'Starting...');
            
            try {
                const response = await fetch('http://localhost:3001/api/mistral/start-mock', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ port: '8001' }) // Use different port to avoid conflicts
                });
                
                const data = await response.json();
                if (data.success) {
                    log(`✅ Mock server started: ${data.message}`, 'success');
                    updateTestResult('Mock Server', 'success', 'Started on port 8001');
                    
                    // Test the mock server
                    setTimeout(async () => {
                        try {
                            const testResponse = await fetch('http://localhost:8001/v1/models');
                            if (testResponse.ok) {
                                log('✅ Mock server responding correctly', 'success');
                            } else {
                                log('⚠️ Mock server not responding as expected', 'warning');
                            }
                        } catch (error) {
                            log(`⚠️ Mock server test failed: ${error.message}`, 'warning');
                        }
                    }, 2000);
                } else {
                    log(`❌ Mock server failed to start: ${data.error}`, 'error');
                    updateTestResult('Mock Server', 'error', 'Failed to start');
                }
            } catch (error) {
                log(`❌ Mock server test failed: ${error.message}`, 'error');
                updateTestResult('Mock Server', 'error', 'Test failed');
            }
        }
        
        async function testFullWorkflow() {
            log('Testing full auto-start workflow...', 'info');
            updateTestResult('Full Workflow', 'info', 'Testing...');
            
            // Step 1: Check auto-start server
            await testAutoStartServer();
            
            // Step 2: Check Docker
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testDockerCheck();
            
            // Step 3: Try mock server
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testMockServer();
            
            // Step 4: Check if any Mistral server is running
            await new Promise(resolve => setTimeout(resolve, 2000));
            try {
                const mistralResponse = await fetch('http://localhost:8000/v1/models');
                if (mistralResponse.ok) {
                    log('✅ Mistral server is running on port 8000', 'success');
                    updateTestResult('Full Workflow', 'success', 'Mistral server ready');
                } else {
                    const mockResponse = await fetch('http://localhost:8001/v1/models');
                    if (mockResponse.ok) {
                        log('✅ Mock server is available as fallback', 'success');
                        updateTestResult('Full Workflow', 'success', 'Mock server ready');
                    } else {
                        log('⚠️ No Mistral server available', 'warning');
                        updateTestResult('Full Workflow', 'warning', 'No server available');
                    }
                }
            } catch (error) {
                log(`⚠️ Server check failed: ${error.message}`, 'warning');
                updateTestResult('Full Workflow', 'warning', 'Server check failed');
            }
        }
        
        // Auto-run tests on page load
        setTimeout(() => {
            log('🚀 Starting automatic tests...', 'info');
            testFullWorkflow();
        }, 1000);
    </script>
</body>
</html>
