<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mistral Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .config-item {
            margin: 10px 0;
        }
        .config-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .config-item input, .config-item select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .test-image {
            max-width: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 Mistral Vision Integration Test</h1>
        <p>This page tests the Mistral Vision API integration for PDF text extraction.</p>
        
        <div class="test-result info">
            <h3>📋 Test Configuration</h3>
            <div class="config-item">
                <label for="mistral-url">Mistral Server URL:</label>
                <input type="text" id="mistral-url" value="http://localhost:8000" />
            </div>
            <div class="config-item">
                <label for="mistral-api-key">API Key (optional):</label>
                <input type="password" id="mistral-api-key" placeholder="Leave empty for local deployment" />
            </div>
            <div class="config-item">
                <label for="mistral-model">Model:</label>
                <select id="mistral-model">
                    <option value="mistral-large-latest">Mistral Large (Latest)</option>
                    <option value="mistral-medium-latest">Mistral Medium (Latest)</option>
                    <option value="mistral-small-latest">Mistral Small (Latest)</option>
                </select>
            </div>
        </div>
        
        <div class="test-result info">
            <h3>🧪 Tests</h3>
            <button onclick="testConnection()">Test Connection</button>
            <button onclick="testModelsEndpoint()">Test Models Endpoint</button>
            <button onclick="testImageUpload()">Test Image Upload</button>
            <button onclick="testTextExtraction()">Test Text Extraction</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="test-result info">
            <h3>📤 Image Upload Test</h3>
            <input type="file" id="test-image-input" accept="image/*" />
            <div id="test-image-preview"></div>
            <button onclick="extractTextFromImage()" disabled id="extract-btn">Extract Text from Image</button>
        </div>
        
        <div class="test-result info">
            <h3>📊 Test Results</h3>
            <div id="test-log" class="log-output">Ready to run tests...</div>
        </div>
    </div>

    <script>
        const testLog = document.getElementById('test-log');
        const extractBtn = document.getElementById('extract-btn');
        const imageInput = document.getElementById('test-image-input');
        const imagePreview = document.getElementById('test-image-preview');
        
        let selectedImageFile = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            testLog.textContent += logEntry;
            testLog.scrollTop = testLog.scrollHeight;
            console.log(`[MISTRAL TEST] ${message}`);
        }
        
        function clearLog() {
            testLog.textContent = 'Log cleared.\n';
        }
        
        function getConfig() {
            return {
                url: document.getElementById('mistral-url').value.trim(),
                apiKey: document.getElementById('mistral-api-key').value.trim(),
                model: document.getElementById('mistral-model').value
            };
        }
        
        async function testConnection() {
            const config = getConfig();
            log('Testing Mistral connection...', 'info');
            
            try {
                const response = await fetch(`${config.url}/v1/models`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Connection successful! Status: ${response.status}`, 'success');
                    log(`Available models: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    log(`❌ Connection failed: ${response.status} ${response.statusText}`, 'error');
                    const errorText = await response.text();
                    log(`Error details: ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ Connection error: ${error.message}`, 'error');
            }
        }
        
        async function testModelsEndpoint() {
            const config = getConfig();
            log('Testing models endpoint...', 'info');
            
            try {
                const response = await fetch(`${config.url}/v1/models`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    }
                });
                
                const data = await response.json();
                log(`Models endpoint response: ${JSON.stringify(data, null, 2)}`, 'info');
                
                if (data.data && Array.isArray(data.data)) {
                    log(`✅ Found ${data.data.length} available models`, 'success');
                    data.data.forEach(model => {
                        log(`  - ${model.id}`, 'info');
                    });
                } else {
                    log('⚠️ Unexpected response format', 'warning');
                }
            } catch (error) {
                log(`❌ Models endpoint error: ${error.message}`, 'error');
            }
        }
        
        function testImageUpload() {
            log('Testing image upload functionality...', 'info');
            imageInput.click();
        }
        
        imageInput.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            selectedImageFile = file;
            log(`📁 Image selected: ${file.name} (${(file.size / 1024).toFixed(2)} KB)`, 'info');
            
            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = `<img src="${e.target.result}" class="test-image" alt="Test image">`;
                extractBtn.disabled = false;
                log('✅ Image preview loaded', 'success');
            };
            reader.readAsDataURL(file);
        });
        
        async function imageToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    const base64 = reader.result.split(',')[1];
                    resolve(base64);
                };
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }
        
        async function extractTextFromImage() {
            if (!selectedImageFile) {
                log('❌ No image selected', 'error');
                return;
            }
            
            const config = getConfig();
            log('🔍 Extracting text from image...', 'info');
            
            try {
                const base64Image = await imageToBase64(selectedImageFile);
                log(`📷 Image converted to base64 (${base64Image.length} characters)`, 'info');
                
                const payload = {
                    model: config.model,
                    messages: [
                        {
                            role: "user",
                            content: [
                                {
                                    type: "text",
                                    text: "Please extract all text from this image. Return only the text content without any additional formatting or explanations."
                                },
                                {
                                    type: "image_url",
                                    image_url: {
                                        url: `data:image/${selectedImageFile.type.split('/')[1]};base64,${base64Image}`
                                    }
                                }
                            ]
                        }
                    ],
                    max_tokens: 4000,
                    temperature: 0.1
                };
                
                log('📤 Sending request to Mistral API...', 'info');
                
                const response = await fetch(`${config.url}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                    },
                    body: JSON.stringify(payload)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log('✅ Text extraction successful!', 'success');
                    
                    if (result.choices && result.choices[0] && result.choices[0].message) {
                        const extractedText = result.choices[0].message.content;
                        log(`📝 Extracted text (${extractedText.length} characters):`, 'info');
                        log(`"${extractedText}"`, 'success');
                    } else {
                        log('⚠️ Unexpected response format', 'warning');
                        log(`Response: ${JSON.stringify(result, null, 2)}`, 'info');
                    }
                } else {
                    log(`❌ Text extraction failed: ${response.status} ${response.statusText}`, 'error');
                    const errorText = await response.text();
                    log(`Error details: ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Text extraction error: ${error.message}`, 'error');
            }
        }
        
        async function testTextExtraction() {
            log('🧪 Testing text extraction with sample image...', 'info');
            
            // Create a simple test image with text
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            
            // White background
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 400, 200);
            
            // Black text
            ctx.fillStyle = 'black';
            ctx.font = '24px Arial';
            ctx.fillText('Hello, Mistral!', 50, 50);
            ctx.fillText('This is a test image.', 50, 100);
            ctx.fillText('Can you read this text?', 50, 150);
            
            // Convert to blob
            canvas.toBlob(async (blob) => {
                selectedImageFile = new File([blob], 'test-image.png', { type: 'image/png' });
                
                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreview.innerHTML = `<img src="${e.target.result}" class="test-image" alt="Generated test image">`;
                };
                reader.readAsDataURL(blob);
                
                log('📷 Generated test image with text', 'info');
                
                // Extract text
                await extractTextFromImage();
            }, 'image/png');
        }
        
        // Auto-run connection test on page load
        setTimeout(() => {
            log('🚀 Starting automatic tests...', 'info');
            testConnection();
        }, 1000);
    </script>
</body>
</html>
