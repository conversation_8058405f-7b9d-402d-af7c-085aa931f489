#!/usr/bin/env python3

"""
Ollama Vision Setup Script
Sets up LLaVA for real vision AI without authentication
"""

import subprocess
import sys
import time
import json
import requests

def check_ollama():
    """Check if Ollama is installed and running"""
    try:
        result = subprocess.run(['ollama', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Ollama installed: {result.stdout.strip()}")
            return True
        else:
            print("❌ Ollama not working properly")
            return False
    except FileNotFoundError:
        print("❌ Ollama not found. Please install from https://ollama.ai/download/windows")
        return False

def start_ollama_service():
    """Start Ollama service"""
    print("🔄 Starting Ollama service...")
    try:
        # On Windows, Ollama should start automatically, but let's check
        result = subprocess.run(['ollama', 'serve'], capture_output=True, text=True, timeout=5)
    except subprocess.TimeoutExpired:
        # This is expected - serve runs in background
        print("✅ Ollama service started")
        return True
    except Exception as e:
        print(f"⚠️ Ollama service may already be running: {e}")
        return True

def check_ollama_api():
    """Check if Ollama API is responding"""
    try:
        response = requests.get('http://localhost:11434/api/tags', timeout=10)
        if response.status_code == 200:
            print("✅ Ollama API is responding")
            return True
        else:
            print(f"❌ Ollama API error: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Ollama API: {e}")
        return False

def list_available_models():
    """List available vision models"""
    models = {
        'llava:7b': {
            'size': '4.7GB',
            'description': 'LLaVA 7B - Good balance of speed and quality',
            'recommended': True
        },
        'llava:13b': {
            'size': '8.0GB', 
            'description': 'LLaVA 13B - Higher quality, slower',
            'recommended': False
        },
        'llava:34b': {
            'size': '20GB',
            'description': 'LLaVA 34B - Best quality, requires powerful hardware',
            'recommended': False
        },
        'bakllava:7b': {
            'size': '4.7GB',
            'description': 'BakLLaVA 7B - Alternative vision model',
            'recommended': False
        },
        'moondream:1.8b': {
            'size': '1.7GB',
            'description': 'Moondream 1.8B - Lightweight, fast',
            'recommended': False
        }
    }
    
    print("\n📋 Available Vision Models:")
    print("=" * 50)
    
    for i, (model, info) in enumerate(models.items(), 1):
        status = "⭐ RECOMMENDED" if info['recommended'] else ""
        print(f"{i}. {model} ({info['size']}) {status}")
        print(f"   {info['description']}")
        print()
    
    return list(models.keys())

def download_model(model_name):
    """Download a model with Ollama"""
    print(f"📥 Downloading {model_name}...")
    print("This may take 5-15 minutes depending on your internet speed")
    print("Please be patient...")
    
    try:
        # Use Popen to show real-time output
        process = subprocess.Popen(
            ['ollama', 'pull', model_name],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Show progress
        for line in process.stdout:
            print(f"  {line.strip()}")
        
        process.wait()
        
        if process.returncode == 0:
            print(f"✅ Successfully downloaded {model_name}!")
            return True
        else:
            print(f"❌ Failed to download {model_name}")
            return False
            
    except Exception as e:
        print(f"❌ Error downloading model: {e}")
        return False

def test_vision_model(model_name):
    """Test the vision model"""
    print(f"🧪 Testing {model_name}...")
    
    test_prompt = "Describe what you see in this image"
    
    try:
        # Test with a simple prompt first
        result = subprocess.run([
            'ollama', 'run', model_name, 
            'Hello! Can you see and describe images?'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Model is responding!")
            print(f"Response: {result.stdout.strip()}")
            return True
        else:
            print("❌ Model test failed")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ Model test timed out (this can be normal for first run)")
        return True
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def show_usage_examples():
    """Show how to use the model"""
    print("\n🎯 How to Use Your Vision AI:")
    print("=" * 40)
    print()
    print("1. Command Line:")
    print("   ollama run llava:7b \"Describe this image: [image_path]\"")
    print()
    print("2. API (for your PDF converter):")
    print("   POST http://localhost:11434/api/generate")
    print("   {")
    print("     \"model\": \"llava:7b\",")
    print("     \"prompt\": \"Extract text from this image\",")
    print("     \"images\": [\"base64_encoded_image\"]")
    print("   }")
    print()
    print("3. In your PDF converter:")
    print("   - Use the Ollama API endpoint")
    print("   - Send PDF page images as base64")
    print("   - Get real OCR results!")
    print()

def main():
    print("🚀 Ollama Vision AI Setup")
    print("=" * 30)
    print()
    
    # Step 1: Check Ollama
    if not check_ollama():
        print("\n❌ Please install Ollama first:")
        print("1. Download from https://ollama.ai/download/windows")
        print("2. Install and restart your terminal")
        print("3. Run this script again")
        return
    
    # Step 2: Start service
    start_ollama_service()
    time.sleep(2)  # Give service time to start
    
    # Step 3: Check API
    if not check_ollama_api():
        print("⚠️ Ollama API not responding. Trying to start...")
        start_ollama_service()
        time.sleep(5)
        if not check_ollama_api():
            print("❌ Cannot connect to Ollama. Please restart Ollama manually.")
            return
    
    # Step 4: Choose model
    models = list_available_models()
    
    print("Which model would you like to download?")
    choice = input("Enter number (1-5) or press Enter for recommended (llava:7b): ").strip()
    
    if not choice:
        model_name = 'llava:7b'
        print(f"Using recommended model: {model_name}")
    elif choice.isdigit() and 1 <= int(choice) <= len(models):
        model_name = models[int(choice) - 1]
        print(f"Selected: {model_name}")
    else:
        print("Invalid choice, using recommended: llava:7b")
        model_name = 'llava:7b'
    
    # Step 5: Download model
    print(f"\n📥 Downloading {model_name}...")
    if download_model(model_name):
        print(f"\n🎉 Success! {model_name} is ready!")
        
        # Step 6: Test model
        test_vision_model(model_name)
        
        # Step 7: Show usage
        show_usage_examples()
        
        print("\n✅ Setup Complete!")
        print(f"Your vision AI model '{model_name}' is ready to use!")
        print("No authentication required - completely offline!")
        
    else:
        print("\n❌ Setup failed. Please try again or check your internet connection.")

if __name__ == "__main__":
    main()
