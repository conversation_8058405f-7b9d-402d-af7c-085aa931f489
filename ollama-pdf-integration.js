/**
 * Ollama PDF Integration
 * Connects your PDF converter to Ollama vision models
 * No authentication required!
 */

class OllamaPDFProcessor {
    constructor(baseUrl = 'http://localhost:11434', model = 'llava:7b') {
        this.baseUrl = baseUrl;
        this.model = model;
    }

    /**
     * Check if Ollama is available
     */
    async checkAvailability() {
        try {
            const response = await fetch(`${this.baseUrl}/api/tags`);
            if (response.ok) {
                const data = await response.json();
                return {
                    available: true,
                    models: data.models || []
                };
            }
            return { available: false, error: 'Ollama not responding' };
        } catch (error) {
            return { available: false, error: error.message };
        }
    }

    /**
     * Process PDF page with Ollama vision
     */
    async processPage(imageBase64, prompt = "Extract all text from this image and format it nicely") {
        try {
            const response = await fetch(`${this.baseUrl}/api/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: this.model,
                    prompt: prompt,
                    images: [imageBase64],
                    stream: false
                })
            });

            if (!response.ok) {
                throw new Error(`Ollama API error: ${response.status}`);
            }

            const data = await response.json();
            return {
                success: true,
                text: data.response,
                model: this.model
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Process multiple PDF pages
     */
    async processPages(pages, onProgress = null) {
        const results = [];
        
        for (let i = 0; i < pages.length; i++) {
            const page = pages[i];
            
            if (onProgress) {
                onProgress(i + 1, pages.length, `Processing page ${i + 1}...`);
            }

            const result = await this.processPage(page.imageBase64, page.prompt);
            
            results.push({
                pageNumber: i + 1,
                ...result
            });

            // Small delay to prevent overwhelming the API
            if (i < pages.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        return results;
    }

    /**
     * Get available models
     */
    async getModels() {
        try {
            const response = await fetch(`${this.baseUrl}/api/tags`);
            if (response.ok) {
                const data = await response.json();
                return data.models || [];
            }
            return [];
        } catch (error) {
            console.error('Error getting models:', error);
            return [];
        }
    }

    /**
     * Set the model to use
     */
    setModel(modelName) {
        this.model = modelName;
    }
}

/**
 * Integration with your existing PDF converter
 */
function integrateOllamaWithPDFConverter() {
    // Add Ollama option to your PDF converter UI
    const ollamaProcessor = new OllamaPDFProcessor();

    // Check if Ollama is available when page loads
    ollamaProcessor.checkAvailability().then(result => {
        if (result.available) {
            console.log('✅ Ollama is available!');
            console.log('Available models:', result.models);
            
            // Enable Ollama option in UI
            enableOllamaOption(result.models);
        } else {
            console.log('❌ Ollama not available:', result.error);
            showOllamaSetupInstructions();
        }
    });

    return ollamaProcessor;
}

function enableOllamaOption(models) {
    // Add this to your PDF converter UI
    const ollamaOption = `
        <div class="ai-option" id="ollama-option">
            <h3>🚀 Ollama Vision AI (No Auth Required!)</h3>
            <p>Real vision AI running locally</p>
            <select id="ollama-model-select">
                ${models.map(model => 
                    `<option value="${model.name}">${model.name} (${(model.size / 1e9).toFixed(1)}GB)</option>`
                ).join('')}
            </select>
            <button onclick="useOllamaForPDF()">Use Ollama AI</button>
        </div>
    `;
    
    // Insert into your existing UI
    document.getElementById('ai-options-container')?.insertAdjacentHTML('beforeend', ollamaOption);
}

function showOllamaSetupInstructions() {
    const instructions = `
        <div class="setup-instructions" id="ollama-setup">
            <h3>🚀 Setup Ollama for Real Vision AI</h3>
            <p>Get real OCR without any authentication!</p>
            <ol>
                <li>Download Ollama: <a href="https://ollama.ai/download/windows" target="_blank">ollama.ai/download/windows</a></li>
                <li>Install and restart your terminal</li>
                <li>Run: <code>python setup-ollama-vision.py</code></li>
                <li>Refresh this page</li>
            </ol>
            <p><strong>Benefits:</strong> Real AI, No authentication, Completely offline</p>
        </div>
    `;
    
    document.getElementById('ai-options-container')?.insertAdjacentHTML('beforeend', instructions);
}

async function useOllamaForPDF() {
    const ollamaProcessor = new OllamaPDFProcessor();
    const selectedModel = document.getElementById('ollama-model-select')?.value || 'llava:7b';
    
    ollamaProcessor.setModel(selectedModel);
    
    // Get PDF pages (assuming you have this function)
    const pdfPages = await getPDFPagesAsImages();
    
    // Process with progress
    const results = await ollamaProcessor.processPages(
        pdfPages.map(page => ({
            imageBase64: page.base64,
            prompt: "Extract all text from this PDF page and format it nicely. Preserve the structure and layout as much as possible."
        })),
        (current, total, message) => {
            updateProgress(current, total, message);
        }
    );
    
    // Combine results into document
    const extractedText = results
        .filter(result => result.success)
        .map(result => `Page ${result.pageNumber}:\n${result.text}\n\n`)
        .join('');
    
    // Display or save the results
    displayExtractedText(extractedText);
    
    console.log('✅ PDF processed with Ollama!');
    console.log(`Model used: ${selectedModel}`);
    console.log(`Pages processed: ${results.length}`);
}

// Export for use in your PDF converter
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        OllamaPDFProcessor,
        integrateOllamaWithPDFConverter,
        useOllamaForPDF
    };
}

// Auto-initialize if in browser
if (typeof window !== 'undefined') {
    window.OllamaPDFProcessor = OllamaPDFProcessor;
    window.integrateOllamaWithPDFConverter = integrateOllamaWithPDFConverter;
    window.useOllamaForPDF = useOllamaForPDF;
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', integrateOllamaWithPDFConverter);
    } else {
        integrateOllamaWithPDFConverter();
    }
}
