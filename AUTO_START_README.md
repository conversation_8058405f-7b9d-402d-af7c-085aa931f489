# 🚀 Mistral Auto-Start Feature

The PDF to Text Converter now includes an **automatic server startup feature** that makes using Mistral Vision incredibly easy!

## ✨ What It Does

When you switch to "Mistral Vision (Local)" in the PDF to Text Converter:

1. **🔍 Checks** if a Mistral server is already running
2. **🐳 Tries Docker** - Attempts to start a Mistral Docker container
3. **🟢 Falls back to Mock** - Starts a testing server if Docker isn't available
4. **📋 Shows Instructions** - Provides manual setup steps if auto-start fails
5. **✅ Ready to Go** - Your server is ready for PDF processing!

## 🎯 Quick Start

### Step 1: Start the Auto-Start Server

**Windows:**
```cmd
start-mistral-auto.bat
```

**Linux/Mac:**
```bash
chmod +x start-mistral-auto.sh
./start-mistral-auto.sh
```

### Step 2: Use the PDF Converter

1. Open your web application at `http://localhost:5173`
2. Navigate to the **PDF to Text Converter** tab
3. Select **"Mistral Vision (Local)"** from the OCR Engine dropdown
4. Watch the magic happen! ✨

The system will automatically:
- Show a status indicator with real-time updates
- Try to start a Mistral server for you
- Provide detailed logs in the conversion log area
- Fall back to a mock server for testing if needed

## 🔧 How It Works

### Auto-Start Server (Port 3001)

The auto-start server runs on `http://localhost:3001` and provides these API endpoints:

- `POST /api/docker/check` - Check if Docker is available
- `POST /api/docker/start-mistral` - Start Mistral Docker container
- `POST /api/mistral/start-mock` - Start mock server for testing
- `GET /api/mistral/status` - Get current server status
- `POST /api/mistral/stop` - Stop running servers

### Frontend Integration

The PDF converter automatically:
1. Detects when you switch to Mistral engine
2. Calls the auto-start server APIs
3. Shows real-time status updates
4. Handles errors gracefully with fallbacks

## 📊 Status Indicators

The UI shows different status indicators:

- 🚀 **Loading**: "Auto-starting Mistral server..."
- ✅ **Success**: "Mistral server started successfully!"
- ⚠️ **Warning**: "Using mock server for testing"
- ❌ **Error**: "Auto-start failed. Manual setup required."

## 🐳 Docker Integration

If Docker is available, the system will:

1. **Check for existing container**: Look for `mistral-server` container
2. **Start existing**: If found, start the existing container
3. **Create new**: If not found, create a new container:
   ```bash
   docker run -d --name mistral-server -p 8000:8000 mistralai/mistral-inference:latest
   ```
4. **Wait for ready**: Monitor until the server responds to health checks

## 🟢 Mock Server Fallback

If Docker isn't available, a **mock server** starts automatically:

- **Purpose**: Testing and development
- **Features**: 
  - Simulates Mistral API responses
  - Provides realistic text extraction results
  - Shows "[MOCK]" prefix in results
  - Includes processing delays for realism

## 🛠️ Manual Setup (If Auto-Start Fails)

If auto-start doesn't work, you'll see manual instructions:

### Option 1: Docker
```bash
docker run -d --name mistral-server -p 8000:8000 mistralai/mistral-inference:latest
```

### Option 2: Direct Installation
```bash
pip install mistral-inference[vision]
# Then run your own server script
```

### Option 3: Alternative APIs
Use any OpenAI-compatible vision API:
- Ollama with vision models
- Local LLaMA with vision
- Other compatible APIs

## 🔍 Troubleshooting

### Common Issues

**1. Auto-start server won't start**
- Check if Node.js is installed: `node --version`
- Install dependencies: `npm install express cors`
- Check port 3001 isn't in use: `netstat -an | grep 3001`

**2. Docker container fails to start**
- Check Docker is running: `docker ps`
- Check port 8000 isn't in use: `netstat -an | grep 8000`
- Try pulling the image manually: `docker pull mistralai/mistral-inference:latest`

**3. Mock server not working**
- Check the conversion log for detailed error messages
- Verify the auto-start server is running on port 3001
- Try restarting the auto-start server

### Debug Steps

1. **Check auto-start server**: Visit `http://localhost:3001/health`
2. **Check Mistral server**: Visit `http://localhost:8000/v1/models`
3. **View logs**: Check the conversion log in the PDF converter
4. **Test manually**: Use the "Test Connection" button

## 📁 File Structure

```
├── mistral-auto-server.js      # Main auto-start server
├── package-mistral.json        # Dependencies for auto-start server
├── start-mistral-auto.bat      # Windows startup script
├── start-mistral-auto.sh       # Linux/Mac startup script
├── AUTO_START_README.md        # This file
└── MISTRAL_SETUP.md           # Detailed setup guide
```

## 🎉 Benefits

- **Zero Configuration**: Works out of the box
- **Intelligent Fallbacks**: Multiple options if one fails
- **Real-time Feedback**: See exactly what's happening
- **Error Recovery**: Graceful handling of failures
- **Testing Support**: Mock server for development
- **Cross-platform**: Works on Windows, Linux, and Mac

## 🔮 Future Enhancements

Planned improvements:
- **GPU Detection**: Automatically use GPU if available
- **Model Selection**: Auto-download different Mistral models
- **Performance Monitoring**: Track processing speed and accuracy
- **Batch Processing**: Handle multiple PDFs simultaneously
- **Cloud Integration**: Hybrid local/cloud processing

## 💡 Tips

1. **First Time**: Let the auto-start run completely before testing
2. **Performance**: Use Docker with GPU support for best results
3. **Testing**: Mock server is perfect for development and testing
4. **Monitoring**: Watch the conversion log for detailed status updates
5. **Patience**: Initial Docker pulls can take several minutes

---

**Happy PDF Converting!** 🎉📄✨
