#!/usr/bin/env python3

"""
Mistral Nemo Direct Downloader
Downloads Mistral Nemo 12B without HuggingFace
"""

import os
import sys
import requests
import tarfile
from pathlib import Path
import hashlib

def download_file(url, filename, expected_md5=None):
    """Download a file with progress tracking"""
    print(f"Downloading {filename}...")
    print(f"URL: {url}")
    
    response = requests.get(url, stream=True)
    response.raise_for_status()
    
    total_size = int(response.headers.get('content-length', 0))
    downloaded = 0
    
    with open(filename, 'wb') as f:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                f.write(chunk)
                downloaded += len(chunk)
                if total_size > 0:
                    percent = (downloaded / total_size) * 100
                    print(f"\rProgress: {percent:.1f}% ({downloaded}/{total_size} bytes)", end='')
    
    print(f"\nDownload complete: {filename}")
    
    # Verify MD5 if provided
    if expected_md5:
        print("Verifying MD5 checksum...")
        with open(filename, 'rb') as f:
            file_hash = hashlib.md5(f.read()).hexdigest()
        
        if file_hash == expected_md5:
            print("MD5 checksum verified!")
        else:
            print(f"MD5 mismatch! Expected: {expected_md5}, Got: {file_hash}")
            return False
    
    return True

def extract_tar(tar_filename, extract_dir):
    """Extract tar file"""
    print(f"Extracting {tar_filename} to {extract_dir}...")
    
    with tarfile.open(tar_filename, 'r') as tar:
        tar.extractall(extract_dir)
    
    print("Extraction complete!")

def download_mistral_nemo():
    """Download Mistral Nemo 12B Instruct"""
    
    # Model details
    model_name = "mistral-nemo-instruct-2407"
    url = "https://models.mistralcdn.com/mistral-nemo-2407/mistral-nemo-instruct-2407.tar"
    expected_md5 = "296fbdf911cb88e6f0be74cd04827fe7"
    
    # Create download directory
    download_dir = Path("./mistral_models")
    download_dir.mkdir(exist_ok=True)
    
    tar_filename = download_dir / f"{model_name}.tar"
    extract_dir = download_dir / model_name
    
    print("Mistral Nemo 12B Direct Download")
    print("=" * 40)
    print(f"Model: {model_name}")
    print(f"Size: ~24GB")
    print(f"No HuggingFace account required!")
    print()
    
    # Download
    try:
        success = download_file(url, tar_filename, expected_md5)
        if not success:
            print("Download failed!")
            return False
        
        # Extract
        extract_tar(tar_filename, extract_dir)
        
        # Clean up tar file
        print("Cleaning up tar file...")
        tar_filename.unlink()
        
        print()
        print("SUCCESS! Mistral Nemo 12B downloaded and ready!")
        print(f"Model location: {extract_dir}")
        print()
        print("You can now use this model for:")
        print("- Text generation")
        print("- Code completion") 
        print("- Question answering")
        print("- Instruction following")
        print()
        print("Note: This is a text-only model (no vision capabilities)")
        print("For vision, you would need Pixtral-12B from HuggingFace")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--download":
        download_mistral_nemo()
    else:
        print("Mistral Nemo Direct Downloader")
        print("=" * 30)
        print()
        print("This downloads Mistral Nemo 12B Instruct directly from Mistral")
        print("No HuggingFace account or token required!")
        print()
        print("Usage:")
        print("  python mistral-nemo-downloader.py --download")
        print()
        print("Model details:")
        print("- Size: ~24GB")
        print("- Capabilities: Text generation, instruction following")
        print("- License: Apache 2.0")
        print("- No vision capabilities (text only)")

if __name__ == "__main__":
    main()
