@echo off
echo 🚀 Starting Mistral Auto-Start Server...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if package.json exists
if not exist "package-mistral.json" (
    echo ❌ package-mistral.json not found
    echo Please ensure you're in the correct directory
    pause
    exit /b 1
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install --package-lock-only=false express cors
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
)

echo ✅ Starting Mistral Auto-Start Server on port 3001...
echo.
echo 📋 The server will provide these features:
echo    - Auto-start Mistral Docker containers
echo    - Fallback mock server for testing
echo    - API endpoints for server management
echo.
echo 🌐 Server will be available at: http://localhost:3001
echo 📖 API Documentation: http://localhost:3001/health
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the server
node mistral-auto-server.js

pause
